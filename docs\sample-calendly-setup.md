Solution: Calendly Inline Embed with Next.js Popup
jsx
// components/CalendlyPopup.jsx
import { useEffect, useState } from 'react';

export default function CalendlyPopup() {
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Load Calendly script dynamically
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <div className="relative">
      {/* Trigger <PERSON> */}
      <button
        onClick={() => setIsOpen(true)}
        className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition"
      >
        Book a Consultation
      </button>

      {/* Popup Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl h-[80vh] relative">
            <button
              onClick={() => setIsOpen(false)}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 z-10"
            >
              ✕
            </button>
            
            {/* Calendly Inline Widget */}
            <div 
              id="calendly-embed" 
              className="calendly-inline-widget h-full w-full"
              data-url="https://calendly.com/your-username/event-type"
              style={{ minWidth: '320px', height: '100%' }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
How to Set Up:
Get your Calendly embed URL:

Go to your Calendly event type

Click "Share" > "Add to Website" > "Inline Embed"

Copy your unique embed URL (looks like https://calendly.com/your-username/event-type)

Add custom fields (if needed):

In Calendly dashboard, edit your event type

Go to "Additional Questions" section

Add custom fields (name, email, etc.)

These will automatically appear in the embed

Add custom CSS (optional):

css
/* globals.css */
.calendly-inline-widget iframe {
  border-radius: 12px !important;
}

.calendly-badge-widget {
  display: none !important;
}

Add event listeners:

jsx
useEffect(() => {
  const handleEvent = (e) => {
    if (e.data.event === 'calendly.event_scheduled') {
      setIsOpen(false);
      // Track conversion in Supabase
      trackBooking(e.data.payload);
    }
  };

  window.addEventListener('message', handleEvent);
  return () => window.removeEventListener('message', handleEvent);
}, []);

Supabase Integration for Tracking:
jsx
// utils/booking.js
import { supabase } from './supabaseClient';

export const trackBooking = async (payload) => {
  await supabase.from('bookings').insert({
    calendly_id: payload.event.uuid,
    event_type: payload.event_type.name,
    invitee_email: payload.invitee.email,
    scheduled_at: payload.event.start_time,
    raw_data: payload
  });
};


Key Advantages:
No redirects - Full in-site experience

No API needed - Works with free Calendly plan

Fully customizable - Control styling with CSS

Conversion tracking - Capture data in Supabase

Mobile responsive - Works on all devices

Security Considerations:
Add Calendly domain to CSP headers in next.config.js:

js
// next.config.js
module.exports = {
  async headers() {
    return [{
      source: '/(.*)',
      headers: [{
        key: 'Content-Security-Policy',
        value: `default-src 'self'; script-src 'self' https://assets.calendly.com; frame-src https://calendly.com; style-src 'self' 'unsafe-inline' https://assets.calendly.com;`
      }]
    }];
  }
}
This implementation gives you a professional booking experience while maintaining full control over the UI/UX, all without paying for Calendly's premium API. The popup loads quickly and provides a seamless user journey from your site to booking completion.


Post-Booking Automation:

jsx
// Listen for booking confirmations
useEffect(() => {
  const handler = (e) => {
    if (e.data.event === 'calendly.event_scheduled') {
      // Store in Supabase
      supabase.from('bookings').insert({
        event_id: e.data.payload.event.uuid,
        invitee: e.data.payload.invitee.email,
        scheduled_at: e.data.payload.event.start_time
      });

      // Trigger custom action
      sendConfirmationEmail(e.data.payload);
    }
  };

  window.addEventListener('message', handler);
  return () => window.removeEventListener('message', handler);
}, []);

