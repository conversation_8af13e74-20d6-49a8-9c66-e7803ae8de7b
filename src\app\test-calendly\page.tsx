'use client'

import { useEffect } from 'react'

export default function TestCalendly() {
  useEffect(() => {
    // Load Calendly script
    const script = document.createElement('script')
    script.src = 'https://assets.calendly.com/assets/external/widget.js'
    script.async = true
    document.body.appendChild(script)

    return () => {
      const existingScript = document.querySelector('script[src*="calendly"]')
      if (existingScript) {
        document.body.removeChild(existingScript)
      }
    }
  }, [])

  return (
    <div style={{ padding: '20px' }}>
      <h1>Calendly Test Page</h1>
      <p>Testing Calendly integration...</p>
      
      <div 
        className="calendly-inline-widget"
        data-url="https://calendly.com/denis-aidev/30min"
        style={{ 
          minWidth: '320px', 
          height: '630px',
          border: '1px solid #ccc',
          borderRadius: '8px'
        }}
      />
    </div>
  )
}
