import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'
import { db } from '@/lib/supabase'

// Calendly webhook event types we handle
const SUPPORTED_EVENTS = [
  'invitee.created',
  'invitee.canceled',
  'invitee_no_show.created'
]

// Verify Calendly webhook signature
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('base64')
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    )
  } catch (error) {
    console.error('Error verifying webhook signature:', error)
    return false
  }
}

// Parse Calendly webhook payload
function parseCalendlyPayload(payload: any) {
  const event = payload.event
  const invitee = payload.payload?.invitee
  const eventDetails = payload.payload?.event

  if (!event || !invitee || !eventDetails) {
    throw new Error('Invalid Calendly webhook payload structure')
  }

  return {
    calendly_id: eventDetails.uuid,
    event_type: eventDetails.event_type?.name || 'Unknown',
    event_name: eventDetails.name,
    invitee_email: invitee.email,
    invitee_name: invitee.name,
    invitee_timezone: invitee.timezone,
    scheduled_at: eventDetails.start_time,
    start_time: eventDetails.start_time,
    end_time: eventDetails.end_time,
    status: event === 'invitee.canceled' ? 'canceled' as const : 
            event === 'invitee_no_show.created' ? 'no_show' as const : 
            'active' as const,
    meeting_url: eventDetails.location?.join_url || eventDetails.location?.location,
    location: eventDetails.location?.type || eventDetails.location?.location,
    raw_data: payload
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the webhook secret from environment variables
    const webhookSecret = process.env.CALENDLY_WEBHOOK_SECRET
    if (!webhookSecret) {
      console.log('CALENDLY_WEBHOOK_SECRET not configured - Free plan detected')
      return NextResponse.json(
        {
          message: 'Webhook functionality requires Calendly paid plan (Essentials or higher)',
          current_plan: 'free',
          alternatives: [
            'Manual data export from Calendly dashboard',
            'Email notification parsing with Zapier',
            'Google Calendar API integration',
            'Upgrade to Calendly Essentials plan ($8/month)'
          ],
          documentation: 'See docs/FREE_PLAN_SETUP.md for alternatives'
        },
        { status: 200 }
      )
    }

    // Get the raw body and signature
    const body = await request.text()
    const signature = request.headers.get('calendly-webhook-signature')

    if (!signature) {
      console.error('Missing Calendly webhook signature')
      return NextResponse.json(
        { error: 'Missing webhook signature' },
        { status: 400 }
      )
    }

    // Verify the webhook signature
    if (!verifyWebhookSignature(body, signature, webhookSecret)) {
      console.error('Invalid Calendly webhook signature')
      return NextResponse.json(
        { error: 'Invalid webhook signature' },
        { status: 401 }
      )
    }

    // Parse the JSON payload
    const payload = JSON.parse(body)
    const eventType = payload.event

    // Check if we support this event type
    if (!SUPPORTED_EVENTS.includes(eventType)) {
      console.log(`Unsupported event type: ${eventType}`)
      return NextResponse.json(
        { message: 'Event type not supported' },
        { status: 200 }
      )
    }

    // Parse the Calendly data
    const bookingData = parseCalendlyPayload(payload)

    // Handle different event types
    switch (eventType) {
      case 'invitee.created':
        // Create new booking
        await db.bookings.upsert(bookingData)
        console.log(`New booking created: ${bookingData.calendly_id}`)
        break

      case 'invitee.canceled':
        // Update booking status to canceled
        await db.bookings.updateStatus(bookingData.calendly_id, 'canceled')
        console.log(`Booking canceled: ${bookingData.calendly_id}`)
        break

      case 'invitee_no_show.created':
        // Update booking status to no_show
        await db.bookings.updateStatus(bookingData.calendly_id, 'no_show')
        console.log(`Booking marked as no-show: ${bookingData.calendly_id}`)
        break

      default:
        console.log(`Unhandled event type: ${eventType}`)
    }

    // Return success response
    return NextResponse.json(
      { 
        message: 'Webhook processed successfully',
        event: eventType,
        calendly_id: bookingData.calendly_id
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Error processing Calendly webhook:', error)
    
    // Return error response
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle GET requests (for webhook verification)
export async function GET() {
  return NextResponse.json(
    { message: 'Calendly webhook endpoint is active' },
    { status: 200 }
  )
}
