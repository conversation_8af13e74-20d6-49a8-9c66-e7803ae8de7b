import { createClient } from '@supabase/supabase-js'

const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// Only create real client if environment variables are properly set
const isProduction = process.env.NODE_ENV === 'production'
const hasValidConfig =
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseAnonKey !== 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Helper to check if Supa<PERSON> is properly configured
export const isSupabaseConfigured = () => hasValidConfig

// Database Types
export interface Contact {
  id: string
  name: string
  email: string
  message: string
  source: string
  created_at: string
}

export interface Analytics {
  id: string
  page: string
  user_agent?: string
  referrer?: string
  ip_address?: string
  created_at: string
}

export interface Booking {
  id: string
  calendly_id: string
  event_type: string
  event_name?: string
  invitee_email: string
  invitee_name?: string
  invitee_timezone?: string
  scheduled_at: string
  start_time: string
  end_time: string
  status: 'active' | 'canceled' | 'no_show'
  meeting_url?: string
  location?: string
  raw_data: any
  created_at: string
  updated_at: string
}

// Database Operations
export const db = {
  // Contact operations
  contacts: {
    async create(contact: Omit<Contact, 'id' | 'created_at'>) {
      const { data, error } = await supabase
        .from('contacts')
        .insert([contact])
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getAll() {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    },

    async getById(id: string) {
      const { data, error } = await supabase
        .from('contacts')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    },
  },

  // Analytics operations
  analytics: {
    async track(analytics: Omit<Analytics, 'id' | 'created_at'>) {
      const { data, error } = await supabase
        .from('analytics')
        .insert([analytics])
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getPageViews(page?: string) {
      let query = supabase
        .from('analytics')
        .select('*')
        .order('created_at', { ascending: false })

      if (page) {
        query = query.eq('page', page)
      }

      const { data, error } = await query

      if (error) throw error
      return data
    },

    async getStats() {
      const { data, error } = await supabase
        .from('analytics')
        .select('page, created_at')
        .gte(
          'created_at',
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        )

      if (error) throw error

      // Process stats
      const pageViews = data.length
      const uniquePages = new Set(data.map(item => item.page)).size
      const dailyViews = data.reduce(
        (acc, item) => {
          const date = new Date(item.created_at).toDateString()
          acc[date] = (acc[date] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      )

      return {
        totalViews: pageViews,
        uniquePages,
        dailyViews,
      }
    },
  },

  // Booking operations
  bookings: {
    async create(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {
      const { data, error } = await supabase
        .from('bookings')
        .insert([booking])
        .select()
        .single()

      if (error) throw error
      return data
    },

    async upsert(booking: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {
      const { data, error } = await supabase
        .from('bookings')
        .upsert([booking], { onConflict: 'calendly_id' })
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getAll() {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .order('scheduled_at', { ascending: false })

      if (error) throw error
      return data
    },

    async getById(id: string) {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('id', id)
        .single()

      if (error) throw error
      return data
    },

    async getByCalendlyId(calendlyId: string) {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('calendly_id', calendlyId)
        .single()

      if (error) throw error
      return data
    },

    async updateStatus(calendlyId: string, status: Booking['status']) {
      const { data, error } = await supabase
        .from('bookings')
        .update({ status })
        .eq('calendly_id', calendlyId)
        .select()
        .single()

      if (error) throw error
      return data
    },

    async getUpcoming(limit = 10) {
      const { data, error } = await supabase
        .from('bookings')
        .select('*')
        .eq('status', 'active')
        .gte('scheduled_at', new Date().toISOString())
        .order('scheduled_at', { ascending: true })
        .limit(limit)

      if (error) throw error
      return data
    },

    async getAnalytics() {
      const { data, error } = await supabase
        .from('booking_analytics')
        .select('*')
        .order('booking_date', { ascending: false })
        .limit(30)

      if (error) throw error
      return data
    },
  },
}

// Real-time subscriptions
export const subscriptions = {
  contacts: (callback: (payload: any) => void) => {
    return supabase
      .channel('contacts')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'contacts' },
        callback
      )
      .subscribe()
  },

  analytics: (callback: (payload: any) => void) => {
    return supabase
      .channel('analytics')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'analytics' },
        callback
      )
      .subscribe()
  },

  bookings: (callback: (payload: any) => void) => {
    return supabase
      .channel('bookings')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'bookings' },
        callback
      )
      .subscribe()
  },
}
