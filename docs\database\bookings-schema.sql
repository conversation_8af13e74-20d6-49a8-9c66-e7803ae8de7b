-- Calendly Bookings Table Schema
-- This file contains the SQL commands to set up the bookings table in Supabase

-- Create the bookings table
CREATE TABLE IF NOT EXISTS bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  calendly_id TEXT UNIQUE NOT NULL,
  event_type TEXT NOT NULL,
  event_name TEXT,
  invitee_email TEXT NOT NULL,
  invitee_name TEXT,
  invitee_timezone TEXT,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'no_show')),
  meeting_url TEXT,
  location TEXT,
  raw_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_bookings_calendly_id ON bookings(calendly_id);
CREATE INDEX IF NOT EXISTS idx_bookings_invitee_email ON bookings(invitee_email);
CREATE INDEX IF NOT EXISTS idx_bookings_scheduled_at ON bookings(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON bookings(created_at);

-- Enable Row Level Security
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Policy for viewing bookings (for future admin dashboard)
CREATE POLICY "Enable read access for authenticated users" ON bookings
  FOR SELECT USING (auth.role() = 'authenticated');

-- Policy for inserting bookings (webhook access)
CREATE POLICY "Enable insert for service role" ON bookings
  FOR INSERT WITH CHECK (true);

-- Policy for updating bookings (webhook access)
CREATE POLICY "Enable update for service role" ON bookings
  FOR UPDATE USING (true);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_bookings_updated_at
  BEFORE UPDATE ON bookings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create a view for booking analytics
CREATE OR REPLACE VIEW booking_analytics AS
SELECT 
  DATE_TRUNC('day', scheduled_at) as booking_date,
  COUNT(*) as total_bookings,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_bookings,
  COUNT(CASE WHEN status = 'canceled' THEN 1 END) as canceled_bookings,
  COUNT(CASE WHEN status = 'no_show' THEN 1 END) as no_show_bookings,
  event_type,
  EXTRACT(HOUR FROM scheduled_at) as booking_hour
FROM bookings
GROUP BY DATE_TRUNC('day', scheduled_at), event_type, EXTRACT(HOUR FROM scheduled_at)
ORDER BY booking_date DESC;

-- Grant necessary permissions
GRANT ALL ON bookings TO authenticated;
GRANT ALL ON bookings TO service_role;
GRANT SELECT ON booking_analytics TO authenticated;
GRANT SELECT ON booking_analytics TO service_role;

-- Insert sample data for testing (optional - remove in production)
-- INSERT INTO bookings (
--   calendly_id,
--   event_type,
--   event_name,
--   invitee_email,
--   invitee_name,
--   invitee_timezone,
--   scheduled_at,
--   start_time,
--   end_time,
--   meeting_url,
--   location,
--   raw_data
-- ) VALUES (
--   'sample-calendly-id-123',
--   '30-minute-consultation',
--   '30 Minute Consultation',
--   '<EMAIL>',
--   'Test User',
--   'America/New_York',
--   '2024-01-15 14:00:00+00',
--   '2024-01-15 14:00:00+00',
--   '2024-01-15 14:30:00+00',
--   'https://calendly.com/events/sample-meeting',
--   'Google Meet',
--   '{"sample": "data"}'::jsonb
-- );
