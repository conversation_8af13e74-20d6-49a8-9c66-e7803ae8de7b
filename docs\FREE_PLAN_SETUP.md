# Calendly Free Plan Setup Guide

## 🎯 Overview

This guide shows how to use the Calendly booking system with a **free Calendly account**. While webhooks aren't available on the free plan, you still get an excellent booking experience with the popup modal.

## ✅ What Works on Free Plan

- ✅ Beautiful popup modal with Calendly embed
- ✅ Seamless user experience (no external redirects)
- ✅ Mobile-responsive booking interface
- ✅ Loading states and error handling
- ✅ All "Book a Call" CTAs replaced with popup
- ✅ Dark glass aesthetic matching your portfolio

## ❌ What Requires Paid Plan

- ❌ Automatic webhook data sync to Supabase
- ❌ Real-time booking notifications
- ❌ Automatic booking analytics
- ❌ Custom automation triggers

## 🚀 Quick Setup for Free Plan

### 1. Environment Configuration

Update your `.env.local` file:

```env
# Your free Calendly scheduling link
NEXT_PUBLIC_CALENDLY_URL=https://calendly.com/your-username/30min

# Leave webhook secret empty for free plan
CALENDLY_WEBHOOK_SECRET=

# Supabase (still needed for other features)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 2. Disable Webhook Features

The system automatically detects when webhook secret is missing and gracefully handles it.

### 3. Test Your Setup

1. Start development server: `npm run dev`
2. Click any "Book a Call" button
3. Verify the modal opens with your Calendly embed
4. Complete a test booking

## 🔄 Alternative Data Collection Methods

### Option 1: Manual Booking Tracking
- Check your Calendly dashboard regularly
- Export booking data manually
- Import to Supabase for analytics

### Option 2: Email Notifications
- Set up Calendly email notifications
- Use email parsing tools (Zapier free tier)
- Forward parsed data to Supabase

### Option 3: Google Calendar Integration
- Connect Calendly to Google Calendar (free)
- Use Google Calendar API to fetch events
- Sync to Supabase with a scheduled job

### Option 4: Upgrade to Paid Plan
- Calendly Essentials: $8/month
- Enables webhooks and advanced features
- Automatic data sync works immediately

## 🛠️ Implementation for Free Plan

### Simplified Webhook Handler

The webhook endpoint will return a helpful message for free plan users:

```typescript
// This is already implemented in your webhook route
if (!webhookSecret) {
  return NextResponse.json({
    message: 'Webhook functionality requires Calendly paid plan',
    alternatives: [
      'Manual data export from Calendly dashboard',
      'Email notification parsing',
      'Google Calendar API integration',
      'Upgrade to Calendly Essentials plan'
    ]
  }, { status: 200 })
}
```

### Manual Data Entry Form (Optional)

You could add a simple form to manually log bookings:

```typescript
// Optional: Manual booking entry for analytics
const manualBookingEntry = {
  invitee_email: '<EMAIL>',
  event_type: '30-minute-consultation',
  scheduled_at: '2024-01-15T14:00:00Z',
  status: 'active'
}
```

## 📊 Analytics Without Webhooks

### Google Analytics Integration
- Track modal opens and closes
- Monitor booking button clicks
- Measure conversion rates

### Calendly Built-in Analytics
- Use Calendly's dashboard analytics
- Export data monthly for reporting
- Track booking trends manually

## 🎯 Recommended Approach

For your current needs, I recommend:

1. **Keep the popup modal system** - it provides excellent UX
2. **Use Calendly's built-in notifications** - you'll get emails for each booking
3. **Check Calendly dashboard weekly** - for booking overview
4. **Consider upgrading later** - when you need automation

## 🔮 Future Upgrade Path

When you're ready to upgrade to Calendly Essentials:

1. **Enable webhook secret** in environment variables
2. **Configure webhook URL** in Calendly dashboard
3. **Automatic sync activates** immediately
4. **All historical data** can be imported

## 💡 Pro Tips for Free Plan

### Maximize Calendly Free Features
- Set up email reminders for invitees
- Customize booking confirmation messages
- Use calendar integrations (Google, Outlook)
- Add buffer times between meetings

### Track Manually
- Keep a simple spreadsheet of bookings
- Note conversion sources (website, social, etc.)
- Track no-shows and cancellations
- Monitor peak booking times

### Optimize Conversion
- A/B test different CTA text
- Monitor which pages generate most bookings
- Optimize modal loading speed
- Ensure mobile experience is perfect

## 🚀 Ready to Deploy

Your booking system is fully functional for free plan users! The popup modal provides an excellent user experience, and you can always add webhook functionality later when you upgrade.

### Deployment Checklist for Free Plan
- [ ] Set `NEXT_PUBLIC_CALENDLY_URL` to your actual link
- [ ] Leave `CALENDLY_WEBHOOK_SECRET` empty
- [ ] Test modal functionality
- [ ] Verify mobile responsiveness
- [ ] Set up Calendly email notifications
- [ ] Deploy to production

The system gracefully handles the absence of webhooks and still provides tremendous value through the improved user experience!
