#!/usr/bin/env node

/**
 * Setup script for Calendly Free Plan users
 * This script helps configure the booking system for free Calendly accounts
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupFreePlan() {
  console.log('🎯 Calendly Free Plan Setup');
  console.log('=====================================\n');
  
  console.log('This script will help you configure the booking system for your free Calendly account.\n');
  
  // Get Calendly URL
  const calendlyUrl = await question('Enter your Calendly scheduling URL (e.g., https://calendly.com/your-username/30min): ');
  
  if (!calendlyUrl.includes('calendly.com')) {
    console.log('❌ Invalid Calendly URL. Please make sure it includes "calendly.com"');
    process.exit(1);
  }
  
  // Check if .env.local exists
  const envPath = path.join(process.cwd(), '.env.local');
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
    console.log('✅ Found existing .env.local file');
  } else {
    console.log('📝 Creating new .env.local file');
  }
  
  // Update or add Calendly URL
  const calendlyUrlLine = `NEXT_PUBLIC_CALENDLY_URL=${calendlyUrl}`;
  
  if (envContent.includes('NEXT_PUBLIC_CALENDLY_URL=')) {
    envContent = envContent.replace(/NEXT_PUBLIC_CALENDLY_URL=.*/, calendlyUrlLine);
  } else {
    envContent += `\n# Calendly Configuration\n${calendlyUrlLine}\n`;
  }
  
  // Ensure webhook secret is commented out for free plan
  if (envContent.includes('CALENDLY_WEBHOOK_SECRET=') && !envContent.includes('# CALENDLY_WEBHOOK_SECRET=')) {
    envContent = envContent.replace(/CALENDLY_WEBHOOK_SECRET=.*/, '# CALENDLY_WEBHOOK_SECRET=your_webhook_secret_when_you_upgrade');
    console.log('📝 Commented out webhook secret (not needed for free plan)');
  } else if (!envContent.includes('CALENDLY_WEBHOOK_SECRET=')) {
    envContent += `# CALENDLY_WEBHOOK_SECRET=your_webhook_secret_when_you_upgrade\n`;
  }
  
  // Write updated .env.local
  fs.writeFileSync(envPath, envContent);
  
  console.log('\n✅ Configuration updated successfully!');
  console.log('\n📋 Next Steps:');
  console.log('1. Start the development server: npm run dev');
  console.log('2. Test the booking modal by clicking "Book a Call"');
  console.log('3. Complete a test booking to verify everything works');
  console.log('\n💡 Free Plan Features:');
  console.log('✅ Beautiful popup modal experience');
  console.log('✅ Mobile-responsive booking interface');
  console.log('✅ No external redirects');
  console.log('❌ Automatic webhook sync (requires paid plan)');
  
  console.log('\n🔮 To enable webhook features:');
  console.log('1. Upgrade to Calendly Essentials ($8/month)');
  console.log('2. Uncomment CALENDLY_WEBHOOK_SECRET in .env.local');
  console.log('3. Configure webhook in Calendly dashboard');
  
  console.log('\n📚 Documentation:');
  console.log('- Free Plan Guide: docs/FREE_PLAN_SETUP.md');
  console.log('- Full Documentation: docs/BOOKING_SYSTEM.md');
  
  rl.close();
}

// Verify we're in the right directory
if (!fs.existsSync('package.json')) {
  console.log('❌ Please run this script from the project root directory');
  process.exit(1);
}

async function main() {
  // Check if this is the right project
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (!packageJson.name.includes('portfolio')) {
    console.log('⚠️ This doesn\'t appear to be the portfolio project');
    const proceed = await question('Continue anyway? (y/N): ');
    if (proceed.toLowerCase() !== 'y') {
      process.exit(0);
    }
  }

  await setupFreePlan();
}

main().catch(error => {
  console.error('❌ Setup failed:', error.message);
  process.exit(1);
});
