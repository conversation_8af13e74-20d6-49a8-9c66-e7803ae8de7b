/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  // Enable strict mode for better development experience
  reactStrictMode: true,

  // Optimize for production
  swcMinify: true,

  // Configure headers for security and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN', // Changed from DENY to allow Calendly embeds
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline' https://assets.calendly.com https://calendly.com https://*.calendly.com",
              "style-src 'self' 'unsafe-inline' https://assets.calendly.com https://*.calendly.com",
              "img-src 'self' data: https: blob:",
              "font-src 'self' data: https://assets.calendly.com https://*.calendly.com",
              "connect-src 'self' https://api.calendly.com https://calendly.com https://*.calendly.com https://*.supabase.co wss://*.supabase.co",
              "frame-src 'self' https://calendly.com https://*.calendly.com",
              "worker-src 'self' blob:",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self' https://calendly.com https://*.calendly.com",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; ')
          }
        ],
      },
    ]
  },

  // Configure redirects if needed
  async redirects() {
    return [
      // Add redirects here if needed
    ]
  },
}

module.exports = nextConfig
