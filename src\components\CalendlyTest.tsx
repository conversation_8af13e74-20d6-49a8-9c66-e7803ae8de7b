'use client'

import { useEffect, useState } from 'react'

export default function CalendlyTest() {
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    // Load Calendly script globally (only once)
    if (!document.querySelector('script[src*="calendly"]')) {
      const script = document.createElement('script')
      script.src = 'https://assets.calendly.com/assets/external/widget.js'
      script.async = true

      script.onload = () => {
        console.log('✅ Calendly script loaded')
        console.log('Window.Calendly:', window.Calendly)
      }

      script.onerror = () => {
        console.error('❌ Failed to load Calendly script')
      }

      document.body.appendChild(script)
    }
  }, [])

  if (!isOpen) {
    return (
      <button 
        onClick={() => setIsOpen(true)}
        className="bg-blue-500 text-white px-4 py-2 rounded"
      >
        Test Calendly
      </button>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl h-[80vh] relative">
        <button
          onClick={() => setIsOpen(false)}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 z-10"
        >
          ✕
        </button>
        
        <div 
          className="calendly-inline-widget h-full w-full"
          data-url="https://calendly.com/denis-aidev/30min"
          style={{ minWidth: '320px', height: '100%' }}
        />
      </div>
    </div>
  )
}

declare global {
  interface Window {
    Calendly?: any
  }
}
