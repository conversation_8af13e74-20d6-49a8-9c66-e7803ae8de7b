# Calendly Booking System Documentation

## 🎯 Overview

This document describes the comprehensive Calendly booking system integration that replaces all external "Book a Call" CTAs with a seamless popup modal experience while maintaining full data sync with Supabase.

## 🏗️ Architecture

### Frontend Components
- **CalendlyBookingModal**: Main modal component with Calendly inline embed
- **useCalendlyModal**: Custom hook for modal state management
- **Updated CTAs**: All booking buttons now trigger the popup modal

### Backend Integration
- **Webhook API**: `/api/calendly/webhook` handles real-time booking sync
- **Database**: `bookings` table stores all booking data
- **Security**: CSP headers configured for Calendly domains

## 📋 Features

### ✅ Implemented Features
- [x] Custom Calendly popup modal with dark glass aesthetic
- [x] Replaced all external Calendly links with popup triggers
- [x] Webhook API for real-time booking data sync
- [x] Comprehensive database schema for booking storage
- [x] Loading states and error handling
- [x] Mobile-responsive design
- [x] Security headers (CSP) for Calendly integration
- [x] Database tables and indexes created in Supabase
- [x] RLS policies and permissions configured
- [x] Analytics view for booking insights
- [x] Comprehensive test suite
- [x] TypeScript interfaces and helper functions

### 🔄 Booking Flow
1. User clicks "Book a Call" button anywhere on the site
2. Calendly modal opens with inline embed
3. User completes booking on Calendly
4. Calendly sends webhook to `/api/calendly/webhook`
5. Booking data is automatically stored in Supabase
6. Modal closes after successful booking

## 🗄️ Database Schema

### Bookings Table
```sql
CREATE TABLE bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  calendly_id TEXT UNIQUE NOT NULL,
  event_type TEXT NOT NULL,
  event_name TEXT,
  invitee_email TEXT NOT NULL,
  invitee_name TEXT,
  invitee_timezone TEXT,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status TEXT DEFAULT 'active',
  meeting_url TEXT,
  location TEXT,
  raw_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🔧 Configuration

### Environment Variables
```env
# Required for Calendly integration
NEXT_PUBLIC_CALENDLY_URL=https://calendly.com/your-scheduling-link
CALENDLY_WEBHOOK_SECRET=your_webhook_secret_from_calendly

# Existing Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Calendly Webhook Setup
1. Go to Calendly Integrations → Webhooks
2. Add webhook URL: `https://yourdomain.com/api/calendly/webhook`
3. Select events: `invitee.created`, `invitee.canceled`, `invitee_no_show.created`
4. Copy webhook secret to environment variables

## 🎨 UI/UX Features

### Modal Design
- Dark glass panel aesthetic matching portfolio design
- Cyan accent colors for consistency
- Smooth animations (fade in, slide up)
- Backdrop blur effect
- Mobile-responsive sizing

### Loading States
- Skeleton loading animation while Calendly loads
- Shimmer effect for better perceived performance
- Loading text feedback

### Error Handling
- Graceful fallback to external Calendly link
- Clear error messaging
- Retry functionality

## 📱 Mobile Optimization

### Responsive Design
- Optimized modal sizing for mobile devices
- Touch-friendly close buttons
- Proper viewport handling
- Reduced height on smaller screens

### Performance
- Lazy loading of Calendly script
- Dynamic script injection only when modal opens
- Cleanup on modal close

## 🔒 Security

### Content Security Policy
```javascript
// CSP headers in next.config.js
"script-src 'self' 'unsafe-eval' 'unsafe-inline' https://assets.calendly.com https://calendly.com"
"frame-src 'self' https://calendly.com https://*.calendly.com"
"connect-src 'self' https://api.calendly.com https://calendly.com"
```

### Webhook Security
- Signature verification using HMAC SHA-256
- Timing-safe comparison to prevent timing attacks
- Environment-based secret management

## 🚀 Deployment

### Database Setup
1. Run the SQL schema in Supabase SQL editor:
   ```bash
   # Execute docs/database/bookings-schema.sql
   ```

2. Configure RLS policies for security

### Webhook Configuration
1. Deploy to production/staging
2. Configure Calendly webhook with deployed URL
3. Test webhook delivery with sample booking

### Environment Variables
- Set all required environment variables in Vercel
- Ensure webhook secret matches Calendly configuration

## 📊 Analytics & Monitoring

### Available Data
- Booking conversion rates
- Popular time slots
- Cancellation patterns
- User timezone distribution

### Database Queries
```sql
-- Get recent bookings
SELECT * FROM bookings ORDER BY created_at DESC LIMIT 10;

-- Get booking analytics
SELECT * FROM booking_analytics ORDER BY booking_date DESC;

-- Get upcoming active bookings
SELECT * FROM bookings 
WHERE status = 'active' 
AND scheduled_at > NOW() 
ORDER BY scheduled_at ASC;
```

## 🔮 Future Enhancements

### Planned Features
- [ ] Admin dashboard for booking management
- [ ] Email automation for booking confirmations
- [ ] CRM integration (HubSpot, Salesforce)
- [ ] Advanced analytics and reporting
- [ ] Custom booking forms with pre-filled data
- [ ] Multiple calendar type support

### Automation Opportunities
- Slack notifications for new bookings
- Automated follow-up email sequences
- Calendar sync with additional platforms
- Lead scoring based on booking behavior

## 🐛 Troubleshooting

### Common Issues

**Modal not opening:**
- Check console for JavaScript errors
- Verify CSP headers allow Calendly domains
- Ensure useCalendlyModal hook is properly imported

**Webhook not receiving data:**
- Verify webhook URL is accessible
- Check webhook secret configuration
- Review Calendly webhook logs

**Calendly not loading:**
- Check network connectivity
- Verify Calendly URL is correct
- Review CSP script-src policies

### Debug Commands
```bash
# Test webhook endpoint
curl -X GET https://yourdomain.com/api/calendly/webhook

# Check database connection
# Run in Supabase SQL editor
SELECT COUNT(*) FROM bookings;
```

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] All tests passing (`node tests/booking-system.test.js`)
- [ ] Environment variables configured in Vercel
- [ ] Supabase database schema deployed
- [ ] CSP headers properly configured
- [ ] TypeScript compilation successful (`npm run type-check`)
- [ ] ESLint checks passing (`npm run lint`)

### Post-Deployment
- [ ] Webhook endpoint accessible at `/api/calendly/webhook`
- [ ] Calendly webhook configured with production URL
- [ ] Test booking flow end-to-end
- [ ] Verify booking data appears in Supabase
- [ ] Test modal functionality on all devices
- [ ] Confirm analytics view is working

### Production Configuration
```bash
# Required environment variables in Vercel
NEXT_PUBLIC_CALENDLY_URL=https://calendly.com/your-actual-link
CALENDLY_WEBHOOK_SECRET=your_production_webhook_secret
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 📞 Support

For issues or questions about the booking system:
1. Check this documentation first
2. Review console logs for errors
3. Test webhook delivery in Calendly dashboard
4. Verify environment variable configuration
5. Run the test suite: `node tests/booking-system.test.js`
