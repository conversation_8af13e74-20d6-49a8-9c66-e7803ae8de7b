'use client'

import { useEffect, useState, useCallback } from 'react'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/denis-aidev/30min'
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [calendlyLoaded, setCalendlyLoaded] = useState(false)

  // Debug: Log the Calendly URL being used
  console.log('Calendly URL:', calendlyUrl)

  // Load Calendly script dynamically
  useEffect(() => {
    if (!isOpen) return

    const loadCalendlyScript = () => {
      // Check if script is already loaded
      if (document.querySelector('script[src*="calendly"]')) {
        setCalendlyLoaded(true)
        setIsLoading(false)
        return
      }

      const script = document.createElement('script')
      script.src = 'https://assets.calendly.com/assets/external/widget.js'
      script.async = true

      script.onload = () => {
        console.log('Calendly script loaded successfully')
        setCalendlyLoaded(true)
        setIsLoading(false)

        // The widget should auto-initialize with data-url attribute
      }

      script.onerror = () => {
        console.error('Failed to load Calendly script')
        setHasError(true)
        setIsLoading(false)
      }

      document.head.appendChild(script)
    }

    loadCalendlyScript()
  }, [isOpen, calendlyUrl])

  // Handle modal close
  const handleClose = useCallback(() => {
    onClose()
    // Reset states when modal closes
    setTimeout(() => {
      setIsLoading(true)
      setHasError(false)
    }, 300)
  }, [onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose])

  // Listen for Calendly events
  useEffect(() => {
    if (!isOpen || !calendlyLoaded) return

    const handleCalendlyEvent = (e: MessageEvent) => {
      if (e.origin !== 'https://calendly.com') return

      if (e.data.event === 'calendly.event_scheduled') {
        // Track booking completion
        console.log('Booking completed:', e.data)

        // Show success message for free plan users
        if (!process.env.CALENDLY_WEBHOOK_SECRET) {
          console.log('📅 Booking completed! Check your Calendly dashboard for details.')
        }

        // Close modal after successful booking
        setTimeout(() => {
          handleClose()
        }, 2000)
      }
    }

    window.addEventListener('message', handleCalendlyEvent)
    return () => window.removeEventListener('message', handleCalendlyEvent)
  }, [isOpen, calendlyLoaded, handleClose])

  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div 
        className="calendly-modal-content" 
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule a Call</h2>
          <button 
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path 
                d="M18 6L6 18M6 6L18 18" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {isLoading && (
            <div className="calendly-loading">
              <div className="calendly-skeleton">
                <div className="skeleton-header"></div>
                <div className="skeleton-calendar"></div>
                <div className="skeleton-times"></div>
              </div>
              <p>Loading calendar...</p>
            </div>
          )}

          {hasError && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try again or use the direct link below.</p>
              <a 
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open in new tab
              </a>
            </div>
          )}

          {!isLoading && !hasError && (
            <div
              className="calendly-inline-widget calendly-widget-container"
              data-url={calendlyUrl}
              style={{ minWidth: '320px', height: '100%' }}
            />
          )}
        </div>
      </div>
    </div>
  )
}

// Extend Window interface for Calendly
declare global {
  interface Window {
    Calendly?: {
      initInlineWidget: (options: {
        url: string
        parentElement: HTMLElement
        prefill?: Record<string, any>
        utm?: Record<string, string>
      }) => void
    }
  }
}


