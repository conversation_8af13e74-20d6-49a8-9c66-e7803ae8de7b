'use client'

import { useEffect, useState } from 'react'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = 'https://calendly.com/denis-aidev/30min'
}: CalendlyBookingModalProps) {
  useEffect(() => {
    // Load Calendly script once globally
    if (!document.querySelector('script[src*="calendly"]')) {
      const script = document.createElement('script')
      script.src = 'https://assets.calendly.com/assets/external/widget.js'
      script.async = true
      document.body.appendChild(script)
    }
  }, [])

  // Handle escape key and body scroll
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'

      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose()
        }
      }

      document.addEventListener('keydown', handleEscape)

      return () => {
        document.body.style.overflow = 'auto'
        document.removeEventListener('keydown', handleEscape)
      }
    }
  }, [isOpen, onClose])

  // Listen for Calendly events
  useEffect(() => {
    const handleCalendlyEvent = (e: MessageEvent) => {
      if (e.data.event === 'calendly.event_scheduled') {
        console.log('📅 Booking completed!')
        setTimeout(() => onClose(), 2000)
      }
    }

    window.addEventListener('message', handleCalendlyEvent)
    return () => window.removeEventListener('message', handleCalendlyEvent)
  }, [onClose])

  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={onClose}>
      <div
        className="calendly-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule a Call</h2>
          <button
            className="calendly-close-btn"
            onClick={onClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          <div
            className="calendly-inline-widget"
            data-url={calendlyUrl}
            style={{ minWidth: '320px', height: '100%' }}
          />
        </div>
      </div>
    </div>
  )
}




