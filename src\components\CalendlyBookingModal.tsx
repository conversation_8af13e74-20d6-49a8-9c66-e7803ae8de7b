'use client'

import { useEffect } from 'react'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/denis-aidev/30min'
}: CalendlyBookingModalProps) {

  useEffect(() => {
    // Load Calendly script dynamically - exactly like the sample
    const script = document.createElement('script')
    script.src = 'https://assets.calendly.com/assets/external/widget.js'
    script.async = true
    document.body.appendChild(script)

    return () => {
      const existingScript = document.querySelector('script[src*="calendly"]')
      if (existingScript) {
        document.body.removeChild(existingScript)
      }
    }
  }, [])

  useEffect(() => {
    // Add event listeners - exactly like the sample
    const handleEvent = (e: MessageEvent) => {
      if (e.data.event === 'calendly.event_scheduled') {
        onClose()
        console.log('📅 Booking completed!')
        // Track conversion in Supabase (for future implementation)
        // trackBooking(e.data.payload)
      }
    }

    window.addEventListener('message', handleEvent)
    return () => window.removeEventListener('message', handleEvent)
  }, [onClose])

  if (!isOpen) return null

  // Popup Modal - exactly like the sample structure
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-3xl h-[80vh] relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 z-10"
        >
          ✕
        </button>

        {/* Calendly Inline Widget - exactly like the sample */}
        <div
          className="calendly-inline-widget h-full w-full"
          data-url={calendlyUrl}
          style={{ minWidth: '320px', height: '100%' }}
        />
      </div>
    </div>
  )
}


